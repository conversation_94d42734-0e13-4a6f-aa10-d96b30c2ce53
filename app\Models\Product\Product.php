<?php

namespace App\Models\Product;

use App\Traits\Product\ProductRelationsTrait;
use App\Traits\Product\ProductAttributesTrait;
use MongoDB\Laravel\Eloquent\Model;

class Product extends Model
{
    use ProductRelationsTrait, ProductAttributesTrait;
    protected $connection = 'mongodb';
    protected $collection = 'products';

    protected $fillable = [
        'title',
        'description',
        'slug',
        'meta_title',
        'meta_description',
        'delivery_method_ids',
        'shop_id',
        'rating',
    ];

    protected $casts = [
        'keyword_id' => 'string',
        'keywordable_id' => 'string',
        'shop_id' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

}
