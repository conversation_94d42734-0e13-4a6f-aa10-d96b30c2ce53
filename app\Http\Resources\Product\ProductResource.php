<?php

namespace App\Http\Resources\Product;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Product\ProductVariationResource;
use App\Http\Resources\Product\GuaranteeResource;
use App\Http\Resources\Product\DeliveryMethodResource;
use App\Models\UserInteraction\Comment;
use Illuminate\Support\Collection;


/**
 * Resource class for transforming Product models into API responses.
 *
 * Provides a comprehensive representation of a product with all its related data,
 * including variations, attributes, gallery images, details, and more.
 */
class ProductResource extends JsonResource
{
    /**
     * Get product attributes to be included directly at the top level of the response
     *
     * @param Collection|null $variations
     * @return array
     */
    private function getProductAttributes(?Collection $variations): array
    {
        if (!$variations) {
            return [];
        }

        // Get all attributes from all variations
        $allAttributes = $variations->flatMap(fn($v) => $v->attributes);

        // Group attributes by type (e.g., 'color', 'size')
        $groupedAttributes = $allAttributes->groupBy('attribute_type');

        $result = [];

        foreach ($groupedAttributes as $attributeType => $attributes) {
            // Get unique attribute values
            $uniqueAttributes = $attributes->unique(fn($item) => $item['attribute_value'])->values();

            // Only add attributes if there are any
            if ($uniqueAttributes->isNotEmpty()) {
                // Format all attributes as simple arrays [x,xl,s] or [red,blue]
                $key = $attributeType . 's'; // Add 's' for pluralization
                $result[$key] = $uniqueAttributes->pluck('attribute_value')->toArray();
            }
        }

        return $result;
    }

    /**
     * Get product attributes formatted as a structured block
     *
     * @param Collection|null $variations
     * @return array
     */
    private function getAttributesBlock(?Collection $variations): array
    {
        if (!$variations) {
            return [];
        }

        // Get all attributes from all variations
        $allAttributes = $variations->flatMap(fn($v) => $v->attributes);

        // Group attributes by type (e.g., 'color', 'size')
        $groupedAttributes = $allAttributes->groupBy('attribute_type');

        $attributesBlock = [];

        foreach ($groupedAttributes as $attributeType => $attributes) {
            // Get unique attribute values with their titles and extra data
            $uniqueAttributes = $attributes->unique(fn($item) => $item['attribute_value'])->values();

            // Only add attributes if there are any
            if ($uniqueAttributes->isNotEmpty()) {
                $attributeValues = $uniqueAttributes->map(function ($attribute) {
                    $data = [
                        'value' => $attribute['attribute_value'],
                    ];

                    // Add extra data if available (like color hex codes)
                    if (!empty($attribute['extra_data'])) {
                        $data['extra_data'] = $attribute['extra_data'];
                    }

                    return $data;
                })->toArray();

                $attributesBlock[] = [
                    'type' => $attributeType,
                    'title' => $uniqueAttributes->first()['attribute_title'] ?? ucfirst($attributeType),
                    'values' => $attributeValues
                ];
            }
        }

        return $attributesBlock;
    }

    /**
     * Get the default variant for the product
     *
     * This method selects the default variant based on the following criteria:
     * 1. The variant with the highest quantity in stock
     * 2. If quantities are equal, the one with the lowest price
     *
     * @param Collection|null $variations
     * @return ProductVariationResource|null
     */
    private function getDefaultVariant(?Collection $variations)
    {
        if (!$variations || $variations->isEmpty()) {
            return null;
        }

        // Sort variations by quantity (descending) and then by price (ascending)
        $sortedVariations = $variations->sortBy([
            ['current_quantity', 'desc'],
            ['price', 'asc']
        ]);

        // Get the first variation after sorting
        $defaultVariant = $sortedVariations->first();

        return $defaultVariant ? new ProductVariationResource($defaultVariant) : null;
    }
    /**
     * Transform the resource into an array.
     *
     * Includes:
     * - Basic product information (title, slug, description)
     * - Meta information (title, description, keywords)
     * - Gallery images
     * - Product details
     * - Variations and default variant
     * - Product attributes (colors, sizes, etc.)
     * - Categories
     * - Guarantees
     * - Delivery methods
     * - Shop information
     * - Comment and question counts
     * - Product rating information
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $variations = $this->whenLoaded('variations', fn() => $this->variations);
        $ratedComments = $this->comments()
            ->where('has_bought', true)
            ->get();
        $productRatesCount = $ratedComments->count();
        $productAverageRate = $productRatesCount > 0
            ? round($ratedComments->avg('rate'), 1)
            : null;

        return [
            'title' => $this->title,
            'slug' => $this->slug,
            'description' => $this->description,
            'comments_count' => $this->comments()->count(),
            'questions_count' => $this->questions()->count(),

            'product_rates_count' => $productRatesCount,
            'product_rate' => $productAverageRate,

            'meta' => [
                'title' => $this->meta_title,
                'description' => $this->meta_description,
                'keywords' => $this->whenLoaded(
                    'keywords',
                    fn() => $this->keywords->pluck('title')->implode(', ')
                ),
            ],
            'galleries' =>
                $this->whenLoaded(
                    'gallery',
                    fn() => $this->gallery->map(fn($pic) => [
                        'url' => $pic->image_url,
                        'caption' => $pic->caption,
                    ])
                ),
            'details' => $this->whenLoaded(
                'details',
                fn() => $this->details->map(fn($detail) => [
                    'key' => $detail->key,
                    'value' => $detail->value,
                ])
            ),

            'variations' => $variations
                ? ProductVariationResource::collection($variations)
                : null,

            'default_variant' => $this->getDefaultVariant($variations),

            // Add attributes block with all attribute types and values
            'attributes' => $this->getAttributesBlock($variations),

            'categories' => $this->whenLoaded(
                'categories',
                fn() => $this->categories->pluck('title')
            ),

            'guarantees' => $this->whenLoaded(
                'guarantees',
                fn() => GuaranteeResource::collection($this->guarantees)
            ),

            'delivery_methods' => $this->whenLoaded(
                'deliveryMethods',
                fn() => DeliveryMethodResource::collection($this->deliveryMethods)
            ),

            'shop' => $this->whenLoaded(
                'shop',
                fn() => [
                    'id' => (string) $this->shop->_id,
                    'title' => $this->shop->title,
                ]
            ),
        ];
    }

}
