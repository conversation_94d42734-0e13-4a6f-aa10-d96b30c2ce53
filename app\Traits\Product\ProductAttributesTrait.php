<?php

namespace App\Traits\Product;

use Illuminate\Support\Str;

/**
 * Product Attributes Trait
 *
 * This trait contains all attribute methods for the Product model.
 * It helps to separate attribute logic from the core model functionality.
 *
 * @package App\Traits\Product
 */
trait ProductAttributesTrait
{
    /**
     * Boot the trait.
     * Automatically generate slug on creating if not provided.
     *
     * @return void
     */
    protected static function bootProductAttributesTrait(): void
    {
        static::creating(function ($product) {
            if (empty($product->slug)) {
                $product->slug = Str::slug($product->title);
            }
        });
    }
}
