<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

/**
 * Main database seeder that orchestrates the execution of all other seeders.
 */
class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * Executes all seeders in the correct order to ensure proper data relationships.
     * The order is important to maintain referential integrity.
     */
    public function run(): void
    {
        $this->call([
            ShopSeeder::class,
            UserSeeder::class,
            ShopUserSeeder::class,
            AttributeSeeder::class, // Add the new AttributeSeeder
            ProductSeeder::class,
            ProductDetailSeeder::class,
            KeywordSeeder::class,
            CategorySeeder::class,
            PurchaseEntrySeeder::class,
            ProductCommentsSeeder::class,
            ProductQuestionsSeeder::class,
            DeliveryMethodSeeder::class,

            GuaranteeSeeder::class,
            ClothProductDataSeeder::class,
            ArticleSeeder::class,
            GuideSeeder::class,
            ProductCategoriesSeeder::class,
            AddressSeeder::class,
            SalePriceSeeder::class,
        ]);
    }
}
