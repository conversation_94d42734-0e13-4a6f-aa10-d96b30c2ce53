<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Common Messages
    |--------------------------------------------------------------------------
    |
    | General messages used throughout the application
    |
    */
    'common' => [
        'success' => 'عملیات با موفقیت انجام شد',
        'created' => 'با موفقیت ایجاد شد',
        'updated' => 'با موفقیت به‌روزرسانی شد',
        'deleted' => 'با موفقیت حذف شد',
        'not_found' => 'یافت نشد',
        'model_not_found' => ':model با شناسه :id یافت نشد',
        'validation_error' => 'خطا در اعتبارسنجی داده‌ها',
        'server_error' => 'خطای سرور',
        'unauthorized' => 'دسترسی غیرمجاز',
        'bad_request' => 'درخواست نامعتبر',
        'forbidden' => 'دسترسی ممنوع است',
        'method_not_allowed' => 'متد درخواست مجاز نیست',
        'too_many_requests' => 'تعداد درخواست‌ها بیش از حد مجاز است',
        'access_denied' => 'شما دسترسی لازم برای این عملیات را ندارید',
    ],

    /*
    |--------------------------------------------------------------------------
    | Authentication Messages
    |--------------------------------------------------------------------------
    |
    | Messages related to authentication and authorization
    |
    */
    'auth' => [
        'failed' => 'اطلاعات ورود صحیح نمی‌باشد.',
        'password' => 'رمز عبور صحیح نمی‌باشد.',
        'throttle' => 'تعداد تلاش‌های ناموفق زیاد بود. لطفا پس از :seconds ثانیه دوباره تلاش کنید.',
        'jwt_error' => 'خطا در احراز هویت: :message',
        'key_not_found' => 'کلید احراز هویت یافت نشد',
        'invalid_key' => 'کلید احراز هویت نامعتبر است',
        'user_not_found' => 'کاربر یافت نشد',
    ],

    /*
    |--------------------------------------------------------------------------
    | User Messages
    |--------------------------------------------------------------------------
    |
    | Messages related to user management
    |
    */
    'user' => [
        'registered' => 'ثبت نام با موفقیت انجام شد',
        'profile_retrieved' => 'اطلاعات کاربر با موفقیت دریافت شد',
        'wallet_balance' => 'موجودی کیف پول',
    ],

    /*
    |--------------------------------------------------------------------------
    | Product Messages
    |--------------------------------------------------------------------------
    |
    | Messages related to product management
    |
    */
    'product' => [
        'found' => 'محصول با موفقیت دریافت شد',
        'not_found' => 'محصول مورد نظر یافت نشد',
        'created' => 'محصول با موفقیت ایجاد شد',
        'updated' => 'محصول با موفقیت به‌روزرسانی شد',
        'deleted' => 'محصول با موفقیت حذف شد',
        'recommendations_found' => 'محصولات مشابه با موفقیت دریافت شدند',
        'recommendations_not_found' => 'محصول مشابهی یافت نشد',
    ],

    /*
    |--------------------------------------------------------------------------
    | Cart Messages
    |--------------------------------------------------------------------------
    |
    | Messages related to shopping cart
    |
    */
    'cart' => [
        'item_added' => 'محصول با موفقیت به سبد خرید اضافه شد',
        'items_added' => 'محصولات با موفقیت به سبد خرید اضافه شدند',
        'items_updated' => 'محصولات سبد خرید با موفقیت به‌روزرسانی شدند',
        'item_updated' => 'تعداد محصول در سبد خرید به‌روزرسانی شد',
        'item_removed' => 'محصول از سبد خرید حذف شد',
        'retrieved' => 'سبد خرید با موفقیت دریافت شد',
        // Validation messages
        'variant_not_found' => 'نوع محصول یافت نشد.',
        'product_not_found' => 'محصول مربوط به این نوع یافت نشد.',
        'insufficient_stock' => 'موجودی کافی برای این نوع محصول وجود ندارد.',
        'exceeds_available_stock' => 'تعداد درخواستی بیشتر از موجودی این نوع محصول است.',

        // Success messages
        'cart_retrieved' => 'سبد خرید با موفقیت دریافت شد.',

        // Error messages
        'cart_empty' => 'سبد خرید خالی است.',
        'item_not_found' => 'محصول در سبد خرید یافت نشد.',
    ],

    /*
    |--------------------------------------------------------------------------
    | Invoice Messages
    |--------------------------------------------------------------------------
    |
    | Messages related to invoices
    |
    */
    'invoice' => [
        'created' => 'فاکتور با موفقیت ایجاد شد',
        'not_created' => 'خطا در ایجاد فاکتور',
        'found' => 'فاکتورهای کاربر با موفقیت دریافت شدند',
        'detail_found' => 'اطلاعات فاکتور با موفقیت دریافت شد',
        'not_found' => 'فاکتوری برای کاربر یافت نشد',
        'not_owned' => 'این فاکتور متعلق به شما نیست',
        'id_required' => 'شناسه فاکتور الزامی است',
        'address_required' => 'انتخاب آدرس برای ایجاد فاکتور الزامی است',
        'address_not_found' => 'آدرس انتخاب شده معتبر نیست',
    ],

    /*
    |--------------------------------------------------------------------------
    | Address Messages
    |--------------------------------------------------------------------------
    |
    | Messages related to user addresses
    |
    */
    'address' => [
        'retrieved' => 'آدرس‌های کاربر با موفقیت دریافت شدند',
        'created' => 'آدرس با موفقیت ایجاد شد',
        'updated' => 'آدرس با موفقیت به‌روزرسانی شد',
        'deleted' => 'آدرس با موفقیت حذف شد',
        'not_found' => 'آدرس مورد نظر یافت نشد.',
        'unauthorized' => 'این آدرس متعلق به کاربر دیگری است.',
    ],

    /*
    |--------------------------------------------------------------------------
    | Article Messages
    |--------------------------------------------------------------------------
    |
    | Messages related to articles
    |
    */
    'article' => [
        'found' => 'مقالات محصول با موفقیت دریافت شدند',
        'not_found' => 'مقاله‌ای برای این محصول یافت نشد',
    ],

    /*
    |--------------------------------------------------------------------------
    | Guide Messages
    |--------------------------------------------------------------------------
    |
    | Messages related to guides
    |
    */
    'guide' => [
        'found' => 'راهنماهای محصول با موفقیت دریافت شدند',
        'not_found' => 'راهنمایی برای این محصول یافت نشد',
    ],

    /*
    |--------------------------------------------------------------------------
    | Comment Messages
    |--------------------------------------------------------------------------
    |
    | Messages related to comments
    |
    */
    'comment' => [
        'found' => 'نظرات محصول با موفقیت دریافت شدند',
        'not_found' => 'نظری برای این محصول یافت نشد',
    ],

    /*
    |--------------------------------------------------------------------------
    | Question Messages
    |--------------------------------------------------------------------------
    |
    | Messages related to questions
    |
    */
    'question' => [
        'found' => 'سوالات محصول با موفقیت دریافت شدند',
        'not_found' => 'سوالی برای این محصول یافت نشد',
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Messages
    |--------------------------------------------------------------------------
    |
    | Messages related to notifications
    |
    */
    'notification' => [
        'created' => 'اعلان با موفقیت ایجاد شد',
        'failed' => 'ایجاد اعلان با خطا مواجه شد',
        'token_stored' => 'توکن با موفقیت ذخیره شد',
        'topic_subscribed' => 'اشتراک در موضوع با موفقیت انجام شد',
        'topic_subscription_failed' => 'اشتراک در موضوع با خطا مواجه شد',
        'future_time_required' => 'زمان ارسال باید در آینده باشد.',
        'invalid_time_format' => 'زمان ارسال باید یک تاریخ و زمان معتبر باشد.',
    ],

    /*
    |--------------------------------------------------------------------------
    | Attribute Messages
    |--------------------------------------------------------------------------
    |
    | Messages related to product attributes
    |
    */
    'attribute' => [
        'found' => 'ویژگی‌های محصول با موفقیت دریافت شدند',
        'not_found' => 'ویژگی‌ای یافت نشد',
    ],

    /*
    |--------------------------------------------------------------------------
    | Transaction Messages
    |--------------------------------------------------------------------------
    |
    | Messages related to payment transactions
    |
    */
    'transaction' => [
        // Success messages
        'created' => 'تراکنش با موفقیت ایجاد شد',
        'invoice_payment_created' => 'عملیات با موفقیت انجام شد',
        'updated' => 'تراکنش با موفقیت به‌روزرسانی شد',
        'verified' => 'تراکنش با موفقیت تایید شد',
        'found' => 'تراکنش‌های فاکتور با موفقیت دریافت شدند',
        'ref_id' => 'شماره پیگیری',
        'refunded' => 'مبلغ تراکنش با موفقیت بازگردانده شد',
        'refunded_due_to_rejection' => 'بازگشت وجه به دلیل رد شدن فاکتور',
        'wallet_refund_for_rejected_invoice' => 'بازگشت وجه به کیف پول برای فاکتور رد شده با شناسه :invoice_id',
        'online_refund_for_rejected_invoice' => 'بازگشت وجه پرداخت آنلاین برای فاکتور رد شده با شناسه :invoice_id',

        // Error messages
        'not_created' => 'خطا در ایجاد تراکنش',
        'not_found' => 'تراکنشی برای این فاکتور یافت نشد',
        'invoice_not_found' => 'فاکتور مورد نظر یافت نشد',
        'invoice_not_owned' => 'این فاکتور متعلق به شما نیست',
        'invoice_already_paid' => 'این فاکتور قابل پرداخت نیست',
        'amount_mismatch' => 'مبلغ تراکنش با مبلغ فاکتور مطابقت ندارد',
        'refund_failed' => 'خطا در بازگرداندن مبلغ تراکنش',

        // Validation messages
        'invoice_required' => 'انتخاب فاکتور برای ایجاد تراکنش الزامی است',
        'payment_method_required' => 'انتخاب روش پرداخت الزامی است',
        'invalid_payment_method' => 'روش پرداخت انتخاب شده معتبر نیست',
        'payment_gateway_required' => 'انتخاب درگاه پرداخت برای پرداخت آنلاین الزامی است',
        'invalid_payment_gateway' => 'درگاه پرداخت انتخاب شده معتبر نیست',
        'amount_required' => 'وارد کردن مبلغ تراکنش الزامی است',
        'amount_numeric' => 'مبلغ تراکنش باید عدد باشد',
        'amount_min' => 'مبلغ تراکنش باید بزرگتر از صفر باشد',
        'status_required' => 'انتخاب وضعیت تراکنش الزامی است',
        'invalid_status' => 'وضعیت تراکنش انتخاب شده معتبر نیست',
        'invalid_paid_date' => 'تاریخ پرداخت باید یک تاریخ معتبر باشد',
        'paid_at_required' => 'وارد کردن تاریخ پرداخت برای تراکنش‌های پرداخت شده الزامی است',
        'track_code_required' => 'وارد کردن کد پیگیری برای تراکنش‌های پرداخت شده الزامی است',
        'payment_identifier_required' => 'وارد کردن حداقل یکی از کد پیگیری، کد مجوز یا شناسه مرجع برای تراکنش‌های پرداخت شده الزامی است',
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Messages
    |--------------------------------------------------------------------------
    |
    | Messages related to payment processing
    |
    */
    'payment' => [
        // Success messages
        'preprocess_success' => 'پیش‌پردازش پرداخت با موفقیت انجام شد',
        'verification_success' => 'تایید پرداخت با موفقیت انجام شد',

        // Error messages
        'preprocess_error' => 'خطا در پیش‌پردازش پرداخت',
        'gateway_error' => 'خطا در ارتباط با درگاه پرداخت',
        'verification_error' => 'خطا در تایید پرداخت',
        'verification_failed' => 'تایید پرداخت با خطا مواجه شد',
        'invalid_amount' => 'مبلغ پرداخت نامعتبر است',
        'invalid_description' => 'توضیحات پرداخت نامعتبر است',
        'invalid_callback' => 'آدرس بازگشت نامعتبر است',
        'missing_parameters' => 'پارامترهای لازم برای پرداخت ارسال نشده‌اند',
        'invalid_response' => 'پاسخ دریافتی از درگاه پرداخت نامعتبر است',
        'unknown_error' => 'خطای ناشناخته در پردازش پرداخت رخ داده است',
    ],

    /*
    |--------------------------------------------------------------------------
    | Wallet Messages
    |--------------------------------------------------------------------------
    |
    | Messages related to wallet functionality
    |
    */
    'wallet' => [
        // Success messages
        'funds_added' => 'مبلغ با موفقیت به کیف پول شما اضافه شد',
        'funds_added_initiated' => 'درخواست افزایش موجودی کیف پول با موفقیت ثبت شد',
        'funds_withdrawn' => 'مبلغ با موفقیت از کیف پول شما برداشت شد',
        'invoice_paid' => 'فاکتور با موفقیت از کیف پول شما پرداخت شد',
        'invoice_partial_paid' => 'فاکتور با موفقیت به صورت ترکیبی از کیف پول و پرداخت آنلاین پرداخت شد',
        'transactions_retrieved' => 'تراکنش‌های کیف پول با موفقیت دریافت شدند',
        'transaction_retrieved' => 'تراکنش کیف پول با موفقیت دریافت شد',
        'balance_retrieved' => 'موجودی کیف پول با موفقیت دریافت شد',

        // Error messages
        'insufficient_balance' => 'موجودی کیف پول شما کافی نیست',
        'zero_balance' => 'موجودی کیف پول شما صفر است. امکان پرداخت با کیف پول وجود ندارد',
        'transaction_not_found' => 'تراکنش کیف پول مورد نظر یافت نشد',

        // Validation messages
        'amount_required' => 'مبلغ الزامی است',
        'amount_numeric' => 'مبلغ باید عددی باشد',
        'amount_min' => 'حداقل مبلغ ۱۰۰۰ تومان است',

        // Description messages
        'deposit_description' => 'افزایش موجودی کیف پول',
        'withdraw_description' => 'برداشت از کیف پول',
        'invoice_payment_description' => 'پرداخت فاکتور شماره :invoice_id از کیف پول',
        'invoice_partial_payment_description' => 'پرداخت بخشی از فاکتور شماره :invoice_id از کیف پول',
        'invoice_remaining_payment_description' => 'پرداخت باقیمانده فاکتور شماره :invoice_id به صورت آنلاین',

        // Transaction types
        'type_deposit' => 'واریز',
        'type_withdraw' => 'برداشت',
    ],
];
