<?php

namespace App\Http\Resources\Product;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource class for transforming product index data into API responses.
 */
class ProductIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * Includes:
     * - Pagination data
     * - Total products count
     * - Min and max prices
     * - Products array with essential information
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $products = $this->resource['products'];

        return [
            'pagination' => [
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
                'per_page' => $products->perPage(),
                'total' => $products->total(),
                'from' => $products->firstItem(),
                'to' => $products->lastItem(),
            ],
            'total_products' => $this->resource['total_count'],
            'min_price' => $this->resource['min_price'],
            'max_price' => $this->resource['max_price'],
            'products' => $products->map(function ($product) {
                // Get the default variation for pricing
                $defaultVariation = $product->variations->first();

                // Use stored product rating
                $productAverageRate = $product->rating ?? 0;

                // Get the main image
                $mainImage = $product->gallery->first();

                // Determine the display price (use sale_price if available and lower than regular price)
                $price = $defaultVariation ? $defaultVariation->price : 0;
                $salePrice = $defaultVariation ? $defaultVariation->sale_price : null;
                $displayPrice = ($salePrice && $salePrice < $price) ? $salePrice : $price;

                return [
                    'title' => $product->title,
                    'slug' => $product->slug,
                    'rate' => $productAverageRate,
                    'image' => $mainImage ? $mainImage->image_url : null,
                    'price' => (int) $displayPrice,
                    'sale_price' => $salePrice && $salePrice < $price ? (int) $salePrice : null,
                ];
            }),
        ];
    }
}
