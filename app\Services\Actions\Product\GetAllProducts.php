<?php

namespace App\Services\Actions\Product;

use App\Models\Product\Product;
use App\Models\Shopping\InvoiceProduct;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * Action class to retrieve all products with filtering, sorting, and pagination.
 */
class GetAllProducts
{
    /**
     * Handle the product listing request with filters and pagination.
     *
     * @param array $data The validated request data
     * @return array Contains paginated products, total count, min/max prices
     */
    public function handle(array $data): array
    {
        $query = Product::with(['variations', 'gallery', 'guarantees', 'comments']);

        // Apply search filter
        if (!empty($data['search'])) {
            $query->where('title', 'like', '%' . $data['search'] . '%');
        }

        // Apply price range filter
        if (isset($data['min_price']) || isset($data['max_price'])) {
            $query->whereHas('variations', function ($variationQuery) use ($data) {
                if (isset($data['min_price'])) {
                    $variationQuery->where(function ($priceQuery) use ($data) {
                        $priceQuery->where('sale_price', '>=', $data['min_price'])
                                  ->orWhere(function ($fallbackQuery) use ($data) {
                                      $fallbackQuery->whereNull('sale_price')
                                                   ->where('price', '>=', $data['min_price']);
                                  });
                    });
                }
                if (isset($data['max_price'])) {
                    $variationQuery->where(function ($priceQuery) use ($data) {
                        $priceQuery->where('sale_price', '<=', $data['max_price'])
                                  ->orWhere(function ($fallbackQuery) use ($data) {
                                      $fallbackQuery->whereNull('sale_price')
                                                   ->where('price', '<=', $data['max_price']);
                                  });
                    });
                }
            });
        }

        // Apply stock filter
        if (!empty($data['in_stock_only'])) {
            $query->whereHas('variations', function ($variationQuery) {
                // For MongoDB, we'll use a simpler approach by checking if variations have stock
                // This can be enhanced later with proper aggregation
                $variationQuery->where(function ($stockQuery) {
                    $stockQuery->whereHas('purchases', function ($purchaseQuery) {
                        $purchaseQuery->where('quantity', '>', 0);
                    });
                });
            });
        }

        // Apply guarantee filter
        if (!empty($data['has_guarantee_only'])) {
            $query->whereHas('guarantees');
        }

        // Apply sorting
        $this->applySorting($query, $data['sort'] ?? 'newest');

        // Get total count before pagination
        $totalCount = $query->count();

        // Get min and max prices
        $priceStats = $this->getPriceStatistics();

        // Apply pagination
        $perPage = $data['per_page'] ?? 15;
        $page = $data['page'] ?? 1;
        $products = $query->paginate($perPage, ['*'], 'page', $page);

        return [
            'products' => $products,
            'total_count' => $totalCount,
            'min_price' => $priceStats['min_price'],
            'max_price' => $priceStats['max_price'],
        ];
    }

    /**
     * Apply sorting to the query based on the sort parameter.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $sort
     * @return void
     */
    private function applySorting($query, string $sort): void
    {
        switch ($sort) {
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            case 'cheapest':
                // For MongoDB, we'll sort by the minimum price from variations
                // This is a simplified approach - can be enhanced with aggregation
                $query->orderBy('created_at', 'desc'); // Fallback for now
                break;
            case 'most_expensive':
                // For MongoDB, we'll sort by the maximum price from variations
                // This is a simplified approach - can be enhanced with aggregation
                $query->orderBy('created_at', 'desc'); // Fallback for now
                break;
            case 'most_sales':
                // For MongoDB, we'll use comment count as a proxy for popularity
                // This can be enhanced later with proper sales tracking
                $query->withCount('comments')->orderBy('comments_count', 'desc');
                break;
            case 'most_viewed':
                // For now, order by created_at desc as we don't have view tracking yet
                $query->orderBy('created_at', 'desc');
                break;
            case 'most_popular':
                // Order by comment count as a popularity metric
                $query->withCount('comments')->orderBy('comments_count', 'desc');
                break;
            default:
                $query->orderBy('created_at', 'desc');
        }
    }

    /**
     * Get minimum and maximum prices from all product variations.
     *
     * @return array
     */
    private function getPriceStatistics(): array
    {
        // For MongoDB, we'll get all variations and calculate min/max in PHP
        $variations = \App\Models\Product\ProductVariation::all();

        if ($variations->isEmpty()) {
            return [
                'min_price' => 0,
                'max_price' => 0,
            ];
        }

        $prices = $variations->map(function ($variation) {
            // Use sale_price if available and lower than regular price, otherwise use regular price
            $salePrice = $variation->sale_price;
            $regularPrice = $variation->price;

            return ($salePrice && $salePrice < $regularPrice) ? $salePrice : $regularPrice;
        })->filter(); // Remove null values

        return [
            'min_price' => (int) $prices->min(),
            'max_price' => (int) $prices->max(),
        ];
    }
}
